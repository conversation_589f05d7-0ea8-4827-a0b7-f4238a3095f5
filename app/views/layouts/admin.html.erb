<!DOCTYPE html>
<html lang="<%= I18n.locale %>">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><%= content_for?(:title) ? yield(:title) : "Admin Panel - Týmbox" %></title>
    <meta name="description" content="Týmbox Admin Panel - System Administration">
    
    <!-- Prevent indexing of admin pages -->
    <meta name="robots" content="noindex, nofollow">
    
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    
    <!-- Admin-specific styles -->
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: #333;
      }
      
      .admin-container {
        display: flex;
        min-height: 100vh;
      }
      
      .admin-sidebar {
        width: 250px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        padding: 2rem 0;
      }
      
      .admin-header {
        padding: 0 2rem 2rem;
        border-bottom: 1px solid #e5e7eb;
        margin-bottom: 2rem;
      }
      
      .admin-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: #4f46e5;
        margin-bottom: 0.5rem;
      }
      
      .admin-user {
        font-size: 0.875rem;
        color: #6b7280;
      }
      
      .admin-nav {
        padding: 0 1rem;
      }
      
      .admin-nav-item {
        display: block;
        padding: 0.75rem 1rem;
        margin-bottom: 0.25rem;
        color: #374151;
        text-decoration: none;
        border-radius: 0.5rem;
        transition: all 0.2s;
      }
      
      .admin-nav-item:hover {
        background: #f3f4f6;
        color: #4f46e5;
      }
      
      .admin-nav-item.active {
        background: #4f46e5;
        color: white;
      }
      
      .admin-nav-icon {
        margin-right: 0.75rem;
        font-size: 1.125rem;
      }
      
      .admin-main {
        flex: 1;
        background: #f8fafc;
        margin: 1rem;
        border-radius: 1rem;
        overflow: hidden;
      }
      
      .admin-topbar {
        background: white;
        padding: 1.5rem 2rem;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        justify-content: between;
        align-items: center;
      }
      
      .admin-content {
        padding: 2rem;
      }
      
      .page-title {
        font-size: 2rem;
        font-weight: bold;
        color: #111827;
        margin-bottom: 0.5rem;
      }
      
      .page-subtitle {
        color: #6b7280;
        margin-bottom: 2rem;
      }
      
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
      }
      
      .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 0.75rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
      
      .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: #4f46e5;
      }
      
      .stat-label {
        color: #6b7280;
        font-size: 0.875rem;
        margin-top: 0.25rem;
      }
      
      .data-table {
        background: white;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
      
      .table-header {
        background: #f9fafb;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
      }
      
      .table-content {
        max-height: 400px;
        overflow-y: auto;
      }
      
      .table-row {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #f3f4f6;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .table-row:last-child {
        border-bottom: none;
      }
      
      .table-row:hover {
        background: #f9fafb;
      }
      
      .btn {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
      }
      
      .btn-primary {
        background: #4f46e5;
        color: white;
      }
      
      .btn-primary:hover {
        background: #4338ca;
      }
      
      .btn-secondary {
        background: #6b7280;
        color: white;
      }
      
      .btn-secondary:hover {
        background: #4b5563;
      }
      
      .alert {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
      }
      
      .alert-error {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
      }
      
      .alert-success {
        background: #f0fdf4;
        color: #16a34a;
        border: 1px solid #bbf7d0;
      }
      
      .exit-admin {
        margin-top: 2rem;
        padding: 0 1rem;
      }
      
      .exit-admin-link {
        display: block;
        padding: 0.75rem 1rem;
        color: #dc2626;
        text-decoration: none;
        border-radius: 0.5rem;
        transition: all 0.2s;
        border: 1px solid #fecaca;
      }
      
      .exit-admin-link:hover {
        background: #fef2f2;
      }
      
      @media (max-width: 768px) {
        .admin-container {
          flex-direction: column;
        }
        
        .admin-sidebar {
          width: 100%;
          order: 2;
        }
        
        .admin-main {
          margin: 0;
          border-radius: 0;
          order: 1;
        }
      }
    </style>
  </head>

  <body>
    <div class="admin-container">
      <!-- Admin Sidebar -->
      <div class="admin-sidebar">
        <div class="admin-header">
          <div class="admin-title">Admin Panel</div>
          <div class="admin-user"><%= current_user.email %></div>
        </div>
        
        <nav class="admin-nav">
          <%= link_to admin_dashboard_path(locale: I18n.locale), 
                      class: "admin-nav-item #{'active' if controller_name == 'admin' && action_name == 'dashboard'}" do %>
            <span class="admin-nav-icon">📊</span>
            <%= t('admin.dashboard', default: 'Dashboard') %>
          <% end %>
          
          <%= link_to admin_companies_path(locale: I18n.locale), 
                      class: "admin-nav-item #{'active' if controller_name == 'admin' && action_name == 'companies'}" do %>
            <span class="admin-nav-icon">🏢</span>
            <%= t('admin.companies', default: 'Companies') %>
          <% end %>
          
          <%= link_to admin_subscriptions_path(locale: I18n.locale), 
                      class: "admin-nav-item #{'active' if controller_name == 'admin' && action_name == 'subscriptions'}" do %>
            <span class="admin-nav-icon">💳</span>
            <%= t('admin.subscriptions', default: 'Subscriptions') %>
          <% end %>
        </nav>
        
        <div class="exit-admin">
          <%= link_to root_path, class: "exit-admin-link" do %>
            <span class="admin-nav-icon">↩️</span>
            <%= t('admin.exit', default: 'Exit Admin') %>
          <% end %>
        </div>
      </div>
      
      <!-- Admin Main Content -->
      <div class="admin-main">
        <div class="admin-topbar">
          <div>
            <h1 class="page-title"><%= content_for?(:page_title) ? yield(:page_title) : "Admin Panel" %></h1>
            <% if content_for?(:page_subtitle) %>
              <p class="page-subtitle"><%= yield(:page_subtitle) %></p>
            <% end %>
          </div>
          <div>
            <span style="font-size: 0.875rem; color: #6b7280;">
              <%= t('admin.system_time', default: 'System Time') %>: <%= Time.current.strftime("%H:%M:%S") %>
            </span>
          </div>
        </div>
        
        <div class="admin-content">
          <!-- Flash Messages -->
          <% if flash[:error] %>
            <div class="alert alert-error">
              <%= flash[:error] %>
            </div>
          <% end %>
          
          <% if flash[:notice] %>
            <div class="alert alert-success">
              <%= flash[:notice] %>
            </div>
          <% end %>
          
          <!-- Main Content -->
          <%= yield %>
        </div>
      </div>
    </div>
  </body>
</html>
